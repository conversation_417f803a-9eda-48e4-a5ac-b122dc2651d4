import axios from "axios";
import { API_CONFIG } from "../constants";
import type {
  FileListResponse,
  FileDeleteResponse,
  MultipartInitiateResponse,
  MultipartPartUrlResponse,
  CompletedPart,
  MultipartBatchPartUrlsResponse,
  UserPreferences,
  UserStats,
} from "../types/api";

const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT_MS,
  headers: {
    "Content-Type": "application/json",
  },
});

export const fileService = {
  // Multipart endpoints
  async initiateMultipart(filename: string, filesize: number): Promise<MultipartInitiateResponse> {
    const response = await api.post<MultipartInitiateResponse>(`${API_CONFIG.ENDPOINTS.FILES}/multipart/initiate`, { filename, filesize });
    return response.data;
  },

  async getPartUploadUrl(key: string, uploadId: string, partNumber: number): Promise<MultipartPartUrlResponse> {
    const payload: { key: string; upload_id: string; part_number: number } = {
      key,
      upload_id: uploadId,
      part_number: partNumber,
    };
    const response = await api.post<MultipartPartUrlResponse>(`${API_CONFIG.ENDPOINTS.FILES}/multipart/part-url`, payload);
    return response.data;
  },

  async completeMultipart(key: string, uploadId: string, parts: CompletedPart[]): Promise<void> {
    await api.post(`${API_CONFIG.ENDPOINTS.FILES}/multipart/complete`, { key, upload_id: uploadId, parts });
  },

  async abortMultipart(key: string, uploadId: string): Promise<void> {
    await api.post(`${API_CONFIG.ENDPOINTS.FILES}/multipart/abort`, { key, upload_id: uploadId });
  },

  async getBatchPartUploadUrls(key: string, uploadId: string, partNumbers: number[]): Promise<MultipartBatchPartUrlsResponse> {
    const response = await api.post<MultipartBatchPartUrlsResponse>(`${API_CONFIG.ENDPOINTS.FILES}/multipart/part-urls`, {
      key,
      upload_id: uploadId,
      part_numbers: partNumbers,
    });
    return response.data;
  },

  async listFiles(): Promise<FileListResponse> {
    const response = await api.get<FileListResponse>(`${API_CONFIG.ENDPOINTS.FILES}/list`);
    return response.data;
  },

  async deleteFile(key: string): Promise<FileDeleteResponse> {
    const response = await api.delete<FileDeleteResponse>(`${API_CONFIG.ENDPOINTS.FILES}/delete`, { data: { key } });
    return response.data;
  },

  async getDownloadUrl(key: string): Promise<{ download_url: string; filename: string }> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.FILES}/download/${encodeURIComponent(key)}`);
    return response.data;
  },
};

export const userService = {
  async getPreferences(): Promise<UserPreferences> {
    const response = await api.get<UserPreferences>(`${API_CONFIG.ENDPOINTS.USER}/preferences`);
    return response.data;
  },

  async updatePreferences(preferences: Partial<UserPreferences>): Promise<UserPreferences> {
    const response = await api.put<UserPreferences>(`${API_CONFIG.ENDPOINTS.USER}/preferences`, preferences);
    return response.data;
  },

  async verifyEmail(senderEmail: string): Promise<{ status: string; message: string }> {
    try {
      const response = await api.post<{ status: string; message: string }>(
        `${API_CONFIG.ENDPOINTS.USER}/preferences/verify-email?email=${encodeURIComponent(senderEmail)}`
      );
      return response.data;
    } catch (error: unknown) {
      if (axios.isAxiosError(error)) {
        const detail = (error.response?.data as { detail?: string; message?: string } | undefined)?.detail;
        const message = detail || error.response?.data?.message;
        throw new Error(message || "Failed to start verification");
      }
      throw error;
    }
  },

  async getStats(): Promise<UserStats> {
    const response = await api.get<UserStats>(`${API_CONFIG.ENDPOINTS.USER}/stats`);
    return response.data;
  },
};

export const apiClient = api;
export default api;
