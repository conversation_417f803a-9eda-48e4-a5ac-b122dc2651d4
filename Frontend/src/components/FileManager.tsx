import React, { useEffect, useState, useCallback } from "react";
import { Download, Trash2, FileIcon, RefreshCw, CheckSquare, Square, Settings, Loader2 } from "lucide-react";
import { useFileOperations } from "@/hooks/useFileOperations";
import { useFileActions } from "@/hooks/useFileActions";
import { formatFileSize, formatDate } from "@/utils/file";
import { useToast } from "@/hooks/useToast";
import { DataTable, type TableColumn } from "./tables/DataTable";
import Toast from "./Toast";
import ConfirmDialog from "./ConfirmDialog";
import { useConfirmDialog } from "@/hooks/useConfirmDialog";
import { apiClient } from "@/services/api";
import type { FileInfo } from "@/types/api";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { EnhancedAnalysisParametersForm, type AnalysisParameters } from "./analysis/EnhancedAnalysisParametersForm";
import { FileDetailsStep } from "./analysis/FileDetailsStep";
import { AnalysisParametersForm } from "./analysis/AnalysisParametersForm";

interface FileManagerProps {
  refreshTrigger?: number;
}

export const FileManager: React.FC<FileManagerProps> = ({ refreshTrigger }) => {
  const { toast, showToast, hideToast } = useToast();
  const { dialogState, showConfirmDialog } = useConfirmDialog();
  const { files, loading, error, fetchFiles } = useFileOperations();
  const { handleDownload, handleDelete, isOperationInProgress } = useFileActions({
    onDeleteSuccess: fetchFiles,
    showConfirmDialog,
  });

  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [processing, setProcessing] = useState(false);
  const [fetchingMetadata, setFetchingMetadata] = useState(false);
  const [parametersModalOpen, setParametersModalOpen] = useState(false);
  const [modalStep, setModalStep] = useState<"details" | "parameters">("details");
  const [useEnhancedForm, setUseEnhancedForm] = useState(false);
  const [activeFileKey, setActiveFileKey] = useState<string | null>(null);
  const [fileMetadata, setFileMetadata] = useState<{
    channels: string[];
    samplingRate: number;
    duration: number;
  } | null>(null);

  useEffect(() => {
    fetchFiles();
  }, [refreshTrigger, fetchFiles]);

  const handleSelectAll = () => {
    if (selectedFiles.length === files.length) {
      setSelectedFiles([]);
    } else {
      setSelectedFiles(files.map((f) => f.key));
    }
  };

  const handleSelectFile = (fileKey: string) => {
    setSelectedFiles((prev) => (prev.includes(fileKey) ? prev.filter((k) => k !== fileKey) : [...prev, fileKey]));
  };

  const fetchFileMetadata = useCallback(async (fileKey: string) => {
    setFetchingMetadata(true);
    setFileMetadata(null);
    try {
      const response = await apiClient.get(`/files/metadata/${encodeURIComponent(fileKey)}`);
      if (response.data) {
        setFileMetadata({
          channels: response.data.channels || [],
          samplingRate: response.data.sampling_rate || 256,
          duration: response.data.duration_seconds || 600,
        });
      }
    } catch (error) {
      console.error("Failed to fetch file metadata:", error);
      setFileMetadata({
        channels: [],
        samplingRate: 256,
        duration: 600,
      });
    } finally {
      setFetchingMetadata(false);
    }
  }, []);

  const handleActiveFileChange = useCallback(
    async (fileKey: string) => {
      setActiveFileKey(fileKey);
      await fetchFileMetadata(fileKey);
    },
    [fetchFileMetadata]
  );

  const handleProcessFiles = (enhanced = false) => {
    if (selectedFiles.length === 0) {
      showToast("Please select at least one file to process", "error");
      return;
    }

    if (enhanced && selectedFiles.length > 0) {
      const primaryFile = activeFileKey && selectedFiles.includes(activeFileKey) ? activeFileKey : selectedFiles[0];
      void handleActiveFileChange(primaryFile);
    }

    setUseEnhancedForm(enhanced);
    setModalStep(enhanced ? "details" : "parameters");
    setParametersModalOpen(true);
  };

  const buildApiParams = useCallback((parameters: AnalysisParameters, channelList: string[]) => {
    const selectedChannels = parameters.channelSelection?.selectedChannels;
    const resolvedChannels = selectedChannels && selectedChannels.length > 0 ? selectedChannels : channelList;

    return {
      thresholds: {
        amplitude1: parameters.thresholds.amplitude_1,
        amplitude2: parameters.thresholds.amplitude_2,
        peaks1: parameters.thresholds.peaks_1,
        peaks2: parameters.thresholds.peaks_2,
        duration: parameters.thresholds.duration,
        temporal_sync: parameters.thresholds.temporal_sync,
        spatial_sync: parameters.thresholds.spatial_sync,
      },
      montage: parameters.montage,
      frequency: {
        low_cutoff: parameters.frequency.low_cutoff,
        high_cutoff: parameters.frequency.high_cutoff,
      },
      analysis_start: parameters.analysis_start,
      analysis_end: parameters.analysis_end,
      channelSelection: {
        selectedChannels: resolvedChannels,
      },
    };
  }, []);

  const handleSubmitWithParameters = async (params: AnalysisParameters) => {
    setParametersModalOpen(false);
    setModalStep("details");
    setProcessing(true);

    try {
      const getChannelsForFile = (fileKey: string) => {
        if (useEnhancedForm && activeFileKey === fileKey) {
          return fileMetadata?.channels ?? [];
        }

        const file = files.find((f) => f.key === fileKey);
        if (file && "channels" in file) {
          return (file as unknown as { channels?: string[] }).channels ?? [];
        }

        return fileMetadata?.channels ?? [];
      };

      if (useEnhancedForm) {
        if (!activeFileKey) {
          showToast("Select a file to configure before submitting", "error");
          return;
        }

        const channels = getChannelsForFile(activeFileKey);
        const payload = {
          files: [
            {
              file_key: activeFileKey,
              parameters: buildApiParams(params, channels),
            },
          ],
        };

        await apiClient.post("/analysis/batch", payload);

        const processedFile = files.find((file) => file.key === activeFileKey);
        if (processedFile) {
          showToast(`Processing "${processedFile.filename}" with custom parameters. You'll receive an email when complete.`, "success");
        } else {
          showToast("Processing file with custom parameters. You'll receive an email when complete.", "success");
        }

        setSelectedFiles((prev) => prev.filter((key) => key !== activeFileKey));
        setActiveFileKey(null);
        setFileMetadata(null);
      } else {
        if (selectedFiles.length === 0) {
          showToast("Please select at least one file to process", "error");
          return;
        }

        const payload = {
          files: selectedFiles.map((fileKey) => ({
            file_key: fileKey,
            parameters: buildApiParams(params, getChannelsForFile(fileKey)),
          })),
        };

        await apiClient.post("/analysis/batch", payload);

        showToast(`Processing ${selectedFiles.length} file(s) with custom parameters. You'll receive an email when complete.`, "success");

        setSelectedFiles([]);
      }
    } catch {
      showToast("Failed to submit files for processing", "error");
    } finally {
      setProcessing(false);
    }
  };

  const columns: TableColumn<FileInfo>[] = [
    {
      key: "select",
      header: (
        <button
          onClick={handleSelectAll}
          className="p-1 hover:bg-gray-100 rounded"
          title={selectedFiles.length === files.length ? "Deselect All" : "Select All"}
        >
          {selectedFiles.length === files.length ? <CheckSquare className="w-5 h-5 text-blue-600" /> : <Square className="w-5 h-5 text-gray-400" />}
        </button>
      ),
      accessor: (file) => (
        <button onClick={() => handleSelectFile(file.key)} className="p-1 hover:bg-gray-100 rounded">
          {selectedFiles.includes(file.key) ? <CheckSquare className="w-5 h-5 text-blue-600" /> : <Square className="w-5 h-5 text-gray-400" />}
        </button>
      ),
    },
    {
      key: "filename",
      header: "File Name",
      accessor: (file) => (
        <div className="flex items-center">
          <FileIcon className="w-5 h-5 mr-2 text-gray-400" />
          <span className="text-sm font-medium text-gray-900">{file.filename}</span>
        </div>
      ),
    },
    {
      key: "size",
      header: "Size",
      accessor: (file) => <span className="text-sm text-gray-500">{formatFileSize(file.size)}</span>,
    },
    {
      key: "modified",
      header: "Modified",
      accessor: (file) => <span className="text-sm text-gray-500">{formatDate(file.last_modified)}</span>,
    },
    {
      key: "actions",
      header: "Actions",
      className: "text-right",
      accessor: (file) => (
        <div className="flex justify-end gap-2">
          <button
            onClick={() => handleDownload(file.key)}
            disabled={isOperationInProgress("download", file.key)}
            className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
            title="Download"
          >
            {isOperationInProgress("download", file.key) ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Download className="w-4 h-4" />}
          </button>
          <button
            onClick={() => handleDelete(file.key, file.filename)}
            disabled={isOperationInProgress("delete", file.key)}
            className="text-red-600 hover:text-red-900 disabled:opacity-50"
            title="Delete"
          >
            {isOperationInProgress("delete", file.key) ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Trash2 className="w-4 h-4" />}
          </button>
        </div>
      ),
    },
  ];

  return (
    <>
      {toast.isOpen && <Toast message={toast.message} type={toast.type} onClose={hideToast} />}
      <ConfirmDialog {...dialogState} />

      <div className="space-y-4">
        {/* Process Files Button */}
        {selectedFiles.length > 0 && (
          <div className="flex items-center justify-between bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center">
              <CheckSquare className="w-5 h-5 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-gray-900">{selectedFiles.length} file(s) selected</span>
            </div>
            <div className="flex gap-2">
              {processing ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  Processing...
                </>
              ) : fetchingMetadata ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Loading EDF data...
                </>
              ) : (
                <button
                  onClick={() => handleProcessFiles(true)}
                  disabled={processing || fetchingMetadata || selectedFiles.length === 0}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Configure & Analyze
                </button>
              )}
            </div>
          </div>
        )}

        <DataTable
          title={`Files Uploaded (Ready to be Analyzed) (${files.length})`}
          columns={columns}
          data={files}
          loading={loading}
          error={error}
          emptyMessage="No uploaded files available"
          onRefresh={fetchFiles}
          getRowKey={(file) => file.key}
        />
      </div>

      {/* Analysis Parameters Modal */}
      <Dialog
        open={parametersModalOpen}
        onOpenChange={(open) => {
          setParametersModalOpen(open);
          if (!open) setModalStep("details");
        }}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              {modalStep === "details" ? "File Details Overview" : "Configure HFO Analysis Parameters"}
            </DialogTitle>
            <DialogDescription>
              {modalStep === "details"
                ? "Review file information before configuring analysis parameters"
                : useEnhancedForm
                ? "Configure comprehensive parameters matching your analysis requirements"
                : "Customize the parameters for detecting High-Frequency Oscillations (HFOs) in the selected files."}
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4">
            {fetchingMetadata && useEnhancedForm ? (
              <div className="flex flex-col items-center justify-center py-12 space-y-4">
                <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
                <div className="text-center">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Loading EDF File Data</h3>
                  <p className="text-sm text-gray-600">
                    Analyzing {files.filter((f) => selectedFiles.includes(f.key))[0]?.filename || "selected file"}...
                  </p>
                  <p className="text-xs text-gray-500 mt-2">This may take a few seconds.</p>
                </div>
              </div>
            ) : modalStep === "details" && useEnhancedForm ? (
              <FileDetailsStep
                files={files.filter((f) => selectedFiles.includes(f.key))}
                activeFileKey={activeFileKey}
                onFileChange={handleActiveFileChange}
                fetchingMetadata={fetchingMetadata}
                onNext={() => setModalStep("parameters")}
                onCancel={() => {
                  setParametersModalOpen(false);
                  setModalStep("details");
                }}
              />
            ) : useEnhancedForm ? (
              <EnhancedAnalysisParametersForm
                onParametersChange={() => {}}
                onSubmit={handleSubmitWithParameters}
                isSubmitting={processing}
                channels={
                  fileMetadata?.channels || [
                    "FP1-F7",
                    "F7-T3",
                    "T3-T5",
                    "T5-O1",
                    "FP2-F8",
                    "F8-T4",
                    "T4-T6",
                    "T6-O2",
                    "FP1-F3",
                    "F3-C3",
                    "C3-P3",
                    "P3-O1",
                    "FP2-F4",
                    "F4-C4",
                    "C4-P4",
                    "P4-O2",
                  ]
                }
                fileInfo={{
                  filename: files.filter((f) => selectedFiles.includes(f.key))[0]?.filename || "",
                  samplingRate: fileMetadata?.samplingRate || 256,
                  duration: fileMetadata?.duration || 600,
                }}
              />
            ) : (
              <AnalysisParametersForm onParametersChange={() => {}} onSubmit={handleSubmitWithParameters} isSubmitting={processing} channels={[]} />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Loading Overlay */}
      {processing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 flex flex-col items-center gap-4 shadow-xl">
            <Loader2 className="w-12 h-12 animate-spin text-blue-600" />
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Submitting Analysis Request</h3>
              <p className="text-sm text-gray-600">
                Processing {selectedFiles.length} file{selectedFiles.length > 1 ? "s" : ""}...
              </p>
              <p className="text-xs text-gray-500 mt-2">This may take a few moments. You'll receive an email when complete.</p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
