'use client';

import React, { useMemo } from 'react';
import Plot from 'react-plotly.js';
import type { HFOEvent } from '@/types/hfo';

const MAX_POINTS_PER_TRACE = 5000;

const decimateSeries = (time: number[], values: number[], maxPoints: number) => {
  if (time.length <= maxPoints) {
    return { time, values };
  }

  const step = Math.ceil(time.length / maxPoints);
  const decimatedTime: number[] = [];
  const decimatedValues: number[] = [];

  for (let i = 0; i < time.length; i += step) {
    const windowEnd = Math.min(i + step, time.length);
    let extremumIndex = i;
    let extremumValue = values[i];

    for (let j = i + 1; j < windowEnd; j++) {
      if (Math.abs(values[j]) > Math.abs(extremumValue)) {
        extremumIndex = j;
        extremumValue = values[j];
      }
    }

    decimatedTime.push(time[extremumIndex]);
    decimatedValues.push(extremumValue);
  }

  return { time: decimatedTime, values: decimatedValues };
};

interface ChannelRowProps {
  channelName: string;
  data: number[];
  timeWindow: [number, number];
  samplingRate: number;
  height?: number;
  showHFOMarkers?: boolean;
  hfoEvents?: HFOEvent[];
  gain?: number;
  samplingInfo?: {
    downsampled: boolean;
    effective_sampling_rate: number;
    original_sampling_rate: number;
    duration_seconds: number;
    downsample_factor?: number;
  };
}

export const ChannelRow: React.FC<ChannelRowProps> = ({
  channelName,
  data,
  timeWindow,
  samplingRate,
  height = 80,
  showHFOMarkers = false,
  hfoEvents = [],
  gain = 20,
  samplingInfo,
}) => {
  const plotData = useMemo(() => {
    if (!data || data.length === 0) {
      return [];
    }

    // Use effective sampling rate if data was downsampled
    const effectiveSamplingRate = samplingInfo?.downsampled
      ? samplingInfo.effective_sampling_rate
      : samplingRate;

    // Create time axis based on the actual sampling rate of the data
    const timeAxis = data.map((_, i) => i / effectiveSamplingRate);

    // Calculate indices based on the effective sampling rate
    const startIdx = Math.floor(timeWindow[0] * effectiveSamplingRate);
    const endIdx = Math.floor(timeWindow[1] * effectiveSamplingRate);
    const windowedTime = timeAxis.slice(startIdx, endIdx);
    const windowedData = data.slice(startIdx, endIdx);

    const { time: plotTime, values: plotValues } = decimateSeries(windowedTime, windowedData, MAX_POINTS_PER_TRACE);

    const traces: Partial<Plotly.Data>[] = [{
      x: plotTime,
      y: plotValues,
      type: 'scatter',
      mode: 'lines',
      name: channelName,
      line: { color: '#000000', width: 0.8 },
      hovertemplate: `Time: %{x:.2f}s<br>Amplitude: %{y:.2f}μV<extra></extra>`,
    }];

    return traces;
  }, [data, channelName, timeWindow, samplingRate, samplingInfo]);

  const layout = useMemo(() => {
    const shapes: Partial<Plotly.Shape>[] = [];

    if (showHFOMarkers) {
      const channelHFOs = hfoEvents.filter(
        hfo => hfo.channel === channelName &&
        hfo.start_time >= timeWindow[0] &&
        hfo.start_time <= timeWindow[1]
      );


      channelHFOs.forEach(hfo => {
        let color = '#ef4444'; // Red for accepted
        const hfoType = hfo.type || 'accepted';

        if (hfoType === 'rejected' || hfoType === 'rejected_long') {
          color = '#3b82f6'; // Blue for rejected
        } else if (hfoType === 'lfo_rejected') {
          color = '#6b7280'; // Gray for LFO
        } else if (hfoType === 'noise_rejected') {
          color = '#9ca3af'; // Light gray for noise
        }

        // Add vertical line at HFO start time
        shapes.push({
          type: 'line',
          x0: hfo.start_time,
          x1: hfo.start_time,
          y0: 0,
          y1: 1,
          xref: 'x',
          yref: 'paper',
          line: {
            color: color,
            width: 1.5,
            dash: 'solid'
          },
          opacity: 0.7
        });

        // Add shaded region for HFO duration if available
        if (hfo.end_time && hfo.end_time - hfo.start_time > 0.001) {
          shapes.push({
            type: 'rect',
            x0: hfo.start_time,
            x1: hfo.end_time,
            y0: 0,
            y1: 1,
            xref: 'x',
            yref: 'paper',
            fillcolor: color,
            opacity: 0.1,
            line: {
              width: 0
            }
          });
        }
      });
    }

    return {
      xaxis: {
        range: timeWindow,
        showgrid: true,
        gridcolor: '#f0f0f0',
        gridwidth: 0.5,
        showticklabels: false,
        zeroline: false,
        showline: false,
      },
      yaxis: {
        showgrid: false,
        showticklabels: false,
        zeroline: false,
        showline: false,
        autorange: false,
        range: [-gain * 2, gain * 2], // Fixed range based on gain
      },
      height: height,
      margin: { l: 120, r: 10, t: 5, b: 5 },
      hovermode: 'x' as const,
      showlegend: false,
      plot_bgcolor: '#ffffff',
      paper_bgcolor: '#ffffff',
      shapes: shapes,
      annotations: [{
        x: 0,
        y: 0.5,
        xref: 'paper' as const,
        yref: 'paper' as const,
        text: `<b>${channelName}</b><br><span style="font-size:10px">±${gain}μV</span>`,
        showarrow: false,
        xanchor: 'right' as const,
        xshift: -10,
        font: {
          size: 11,
          color: '#4a5568',
          family: 'var(--font-sans), system-ui, -apple-system, sans-serif',
        },
      }],
    };
  }, [channelName, timeWindow, height, showHFOMarkers, hfoEvents, gain]);

  const config = {
    displayModeBar: false,
    responsive: true,
    staticPlot: false,
  };

  return (
    <div className="border-b border-gray-200 hover:bg-gray-50/50 transition-colors">
      <Plot
        data={plotData}
        layout={layout}
        config={config}
        style={{ width: '100%', height: `${height}px` }}
      />
    </div>
  );
};

export default React.memo(ChannelRow);
