import React, { useMemo } from "react";
import Plot from "react-plotly.js";
import type { HFOEvent } from "@/types/hfo";
import { HFO_TYPE_COLORS } from "@/types/hfo";

interface EegSignalPlotProps {
  selectedChannel: string;
  channelData: number[];
  samplingRate: number;
  hfoEvents: HFOEvent[];
  timeWindow: [number, number];
  durationSeconds: number;
  onTimeWindowChange: (window: [number, number]) => void;
}

export const EegSignalPlot: React.FC<EegSignalPlotProps> = ({
  selectedChannel,
  channelData,
  samplingRate,
  hfoEvents,
  timeWindow,
  durationSeconds,
  onTimeWindowChange
}) => {
  const { windowedTime, windowedData } = useMemo(() => {
    if (!channelData || channelData.length === 0) {
      return { windowedTime: [] as number[], windowedData: [] as number[] };
    }

    const timeAxis = channelData.map((_, i) => i / samplingRate);
    const startIdx = Math.max(0, Math.floor(timeWindow[0] * samplingRate));
    const endIdx = Math.min(channelData.length, Math.floor(timeWindow[1] * samplingRate));

    return {
      windowedTime: timeAxis.slice(startIdx, endIdx),
      windowedData: channelData.slice(startIdx, endIdx)
    };
  }, [channelData, samplingRate, timeWindow]);

  const channelHFOs = useMemo(() => {
    return hfoEvents.filter(
      (hfo) =>
        hfo.channel === selectedChannel &&
        hfo.start_time >= timeWindow[0] &&
        hfo.start_time <= timeWindow[1]
    );
  }, [hfoEvents, selectedChannel, timeWindow]);

  const plotData = useMemo(() => {
    if (windowedTime.length === 0 || windowedData.length === 0) {
      return [];
    }

    return [
      {
        type: "scatter" as const,
        mode: "lines" as const,
        name: selectedChannel,
        x: windowedTime,
        y: windowedData,
        line: { color: "#000000", width: 0.8 },
        hovertemplate: `Time: %{x:.2f}s<br>Amplitude: %{y:.2f}μV<extra></extra>`
      }
    ];
  }, [selectedChannel, windowedTime, windowedData]);

  const hfoShapes = useMemo(() => {
    const shapes: Partial<Plotly.Shape>[] = [];

    channelHFOs.forEach((hfo) => {
      const color = HFO_TYPE_COLORS[hfo.type] ?? "#3b82f6";
      const dash = hfo.type === "rejected_long" ? "dot" : "solid";

      shapes.push({
        type: "line",
        x0: hfo.start_time,
        x1: hfo.start_time,
        y0: 0,
        y1: 1,
        xref: "x",
        yref: "paper",
        line: {
          color,
          width: 1.5,
          dash
        },
        opacity: 0.7
      });

      if (hfo.end_time && hfo.end_time - hfo.start_time > 0.001) {
        shapes.push({
          type: "rect",
          x0: hfo.start_time,
          x1: hfo.end_time,
          y0: 0,
          y1: 1,
          xref: "x",
          yref: "paper",
          fillcolor: color,
          opacity: 0.12,
          line: {
            width: 0
          }
        });
      }
    });

    return shapes;
  }, [channelHFOs]);

  const layout: Partial<Plotly.Layout> = useMemo(() => ({
    height: 300,
    xaxis: {
      range: timeWindow,
      showgrid: false,
      showticklabels: false,
      zeroline: false,
      showline: false,
      title: { text: "Time (seconds)" },
      tickfont: {
        family: "var(--font-sans), system-ui, -apple-system, sans-serif",
        size: 11,
        color: "#64748b"
      }
    },
    yaxis: {
      showgrid: false,
      showticklabels: false,
      zeroline: false,
      showline: false,
      title: { text: "Amplitude (μV)" },
      tickfont: {
        family: "var(--font-sans), system-ui, -apple-system, sans-serif",
        size: 11,
        color: "#64748b"
      },
      autorange: true
    },
    margin: { l: 80, r: 20, t: 30, b: 60 },
    hovermode: "x" as const,
    showlegend: false,
    plot_bgcolor: "#ffffff",
    paper_bgcolor: "#ffffff",
    shapes: hfoShapes,
    annotations: [
      {
        x: 0,
        y: 1.1,
        xref: "paper" as const,
        yref: "paper" as const,
        text: selectedChannel,
        showarrow: false,
        xanchor: "left" as const,
        font: {
          size: 14,
          color: "#1f2937",
          family: "var(--font-sans), system-ui, -apple-system, sans-serif"
        }
      }
    ]
  }), [hfoShapes, selectedChannel, timeWindow]);

  return (
    <div className="border rounded-lg p-4">
      <Plot
        data={plotData}
        layout={layout}
        config={{ responsive: true }}
        style={{ width: "100%" }}
      />

      {/* Time Window Controls */}
      <div className="mt-4 flex items-center space-x-4">
        <button
          onClick={() =>
            onTimeWindowChange([Math.max(0, timeWindow[0] - 10), timeWindow[1] - 10])
          }
          disabled={timeWindow[0] <= 0}
          className="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50"
        >
          ← Previous
        </button>

        <span className="text-sm text-gray-600">
          {timeWindow[0].toFixed(1)}s - {timeWindow[1].toFixed(1)}s
        </span>

        <button
          onClick={() =>
            onTimeWindowChange([timeWindow[0] + 10, timeWindow[1] + 10])
          }
          disabled={timeWindow[1] >= durationSeconds}
          className="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50"
        >
          Next →
        </button>

        <input
          type="range"
          min="1"
          max="60"
          value={timeWindow[1] - timeWindow[0]}
          onChange={(e) => {
            const windowSize = parseInt(e.target.value);
            onTimeWindowChange([timeWindow[0], timeWindow[0] + windowSize]);
          }}
          className="ml-4"
        />
        <span className="text-sm text-gray-600">
          Window: {timeWindow[1] - timeWindow[0]}s
        </span>
      </div>
    </div>
  );
};
